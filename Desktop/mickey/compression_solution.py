def compressedString(message):
    """
    Compresses a string by indicating consecutive occurrences of each character.
    
    Args:
        message (str): The input string to compress
        
    Returns:
        str: The compressed string
    """
    if not message:
        return ""
    
    compressed = []
    current_char = message[0]
    count = 1
    for i in range(1, len(message)):
        if message[i] == current_char:
            count += 1
        else:
            if count == 1:
                compressed.append(current_char)
            else:
                compressed.append(current_char + str(count))
            current_char = message[i]
            count = 1
    if count == 1:
        compressed.append(current_char)
    else:
        compressed.append(current_char + str(count))
    
    return ''.join(compressed)


# Test function
def test_compression():
    """Test the compression function with various inputs"""
    
    test_cases = [
        ("abaasass", "aba2sas2"),
        ("abc", "abc"),
        ("aaabbbccc", "a3b3c3"),
        ("aabbcc", "a2b2c2"),
        ("abcdef", "abcdef"),
        ("aaaa", "a4"),
        ("a", "a"),
        ("", ""),
        ("aabbccddee", "a2b2c2d2e2"),
        ("abcabcabc", "abcabcabc")
    ]
    
    print("Testing compression function:")
    print("-" * 50)
    
    for i, (input_str, expected) in enumerate(test_cases):
        result = compressedString(input_str)
        status = "✓ PASS" if result == expected else "✗ FAIL"
        print(f"Test {i+1}: {status}")
        print(f"  Input:    '{input_str}'")
        print(f"  Expected: '{expected}'")
        print(f"  Got:      '{result}'")
        print()


if __name__ == "__main__":
    test_compression()
