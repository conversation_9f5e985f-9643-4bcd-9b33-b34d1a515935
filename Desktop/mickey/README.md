# String Compression Interview Solution

This repository contains a complete solution to the string compression interview question.

## 📁 Files

- **`compression_solution.py`** - Main solution with the `compressedString()` function and comprehensive tests
- **`compression_solution.md`** - Detailed explanation of the algorithm, approach, and complexity analysis
- **`demo.py`** - Interactive demo script to test the compression algorithm
- **`README.md`** - This file

## 🚀 Quick Start

### Run the tests:
```bash
python compression_solution.py
```

### Try the interactive demo:
```bash
python demo.py
```

## 🧮 Algorithm Summary

The solution uses a **single-pass algorithm** with O(n) time complexity:

1. Track consecutive character occurrences
2. Add single characters as-is
3. Add repeated characters with their count
4. Handle edge cases (empty string, single character, etc.)

## 📊 Example

```
Input:  "abaasass"
Output: "aba2sas2"

Breakdown:
- 'a' (1 time) → 'a'
- 'b' (1 time) → 'b'  
- 'aa' (2 times) → 'a2'
- 's' (1 time) → 's'
- 'a' (1 time) → 'a'
- 'ss' (2 times) → 's2'
```

## ✅ Test Results

All test cases pass:
- Basic compression: `"abaasass"` → `"aba2sas2"`
- No compression needed: `"abc"` → `"abc"`
- All consecutive: `"aaabbbccc"` → `"a3b3c3"`
- Edge cases: empty string, single character, etc.

## 🎯 Interview Tips

1. **Start with examples** - Walk through the given example step by step
2. **Handle edge cases** - Empty string, single character, no consecutive chars
3. **Optimize incrementally** - Start with a working solution, then optimize
4. **Test thoroughly** - Include various test cases to validate your solution
5. **Explain complexity** - Discuss time O(n) and space O(n) complexity

## 🔧 Alternative Approaches

The solution file also includes discussion of alternative approaches:
- Two-pointer technique
- Python's `itertools.groupby()`
- Trade-offs between readability and performance
