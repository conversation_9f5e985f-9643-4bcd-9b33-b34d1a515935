# String Compression Algorithm Solution

## Problem Statement

Implement a compression algorithm that compresses a string by indicating the total number of consecutive occurrences of each character.

### Rules:
- If a character occurs only once consecutively, add just the character
- If a character occurs multiple times consecutively, add the character followed by the count
- Process characters from left to right

### Example:
- Input: `"abaasass"`
- Output: `"aba2sas2"`

## Algorithm Explanation

### Approach: Single Pass with Character Counting

The solution uses a **single-pass algorithm** that tracks consecutive character occurrences:

1. **Initialize**: Start with the first character and count = 1
2. **Iterate**: Compare each subsequent character with the current character
3. **Count**: If same, increment count; if different, process the current group
4. **Process**: Add character (+ count if > 1) to result, reset for new character
5. **Finalize**: Handle the last group after the loop

### Time Complexity: O(n)
- Single pass through the input string
- Each character is processed exactly once

### Space Complexity: O(n)
- In worst case (no consecutive characters), output size equals input size
- Additional space for the result array

## Code Implementation

```python
def compressedString(message):
    if not message:
        return ""
    
    compressed = []
    current_char = message[0]
    count = 1
    
    # Process each character starting from index 1
    for i in range(1, len(message)):
        if message[i] == current_char:
            count += 1
        else:
            # Add current group to result
            if count == 1:
                compressed.append(current_char)
            else:
                compressed.append(current_char + str(count))
            
            # Reset for new character
            current_char = message[i]
            count = 1
    
    # Handle the last group
    if count == 1:
        compressed.append(current_char)
    else:
        compressed.append(current_char + str(count))
    
    return ''.join(compressed)
```

## Step-by-Step Walkthrough

### Example: `"abaasass"`

| Step | Current Char | Count | Next Char | Action | Result So Far |
|------|-------------|-------|-----------|---------|---------------|
| 1    | 'a'         | 1     | 'b'       | Add 'a' | "a"           |
| 2    | 'b'         | 1     | 'a'       | Add 'b' | "ab"          |
| 3    | 'a'         | 1     | 'a'       | Count++ | "ab"          |
| 4    | 'a'         | 2     | 's'       | Add 'a2'| "aba2"        |
| 5    | 's'         | 1     | 'a'       | Add 's' | "aba2s"       |
| 6    | 'a'         | 1     | 's'       | Add 'a' | "aba2sa"      |
| 7    | 's'         | 1     | 's'       | Count++ | "aba2sa"      |
| 8    | 's'         | 2     | END       | Add 's2'| "aba2sas2"    |

## Edge Cases Handled

1. **Empty string**: Returns empty string
2. **Single character**: Returns the character itself
3. **No consecutive characters**: Returns original string
4. **All same characters**: Returns character + count
5. **Mixed patterns**: Handles alternating single and multiple occurrences

## Test Cases

```python
test_cases = [
    ("abaasass", "aba2sas2"),    # Mixed pattern
    ("abc", "abc"),              # No consecutive chars
    ("aaabbbccc", "a3b3c3"),     # All consecutive groups
    ("a", "a"),                  # Single character
    ("", ""),                    # Empty string
    ("aaaa", "a4"),              # All same character
]
```

## Alternative Approaches

### 1. Two-Pointer Technique
- Use start and end pointers to identify consecutive groups
- More complex but potentially clearer logic separation

### 2. Groupby (Python-specific)
```python
from itertools import groupby
def compressedString(message):
    result = []
    for char, group in groupby(message):
        count = len(list(group))
        result.append(char if count == 1 else char + str(count))
    return ''.join(result)
```

## Performance Analysis

- **Best Case**: O(n) time, O(1) extra space (all characters same)
- **Average Case**: O(n) time, O(n) space
- **Worst Case**: O(n) time, O(n) space (no compression possible)

The algorithm is optimal for this problem as we must examine every character at least once.
