#!/usr/bin/env python3
"""
Interactive demo for the string compression algorithm
"""

from compression_solution import compressedString

def main():
    print("🗜️  String Compression Algorithm Demo")
    print("=" * 50)
    print("This algorithm compresses strings by counting consecutive characters.")
    print("Examples:")
    print("  'abaasass' → 'aba2sas2'")
    print("  'abc' → 'abc'")
    print("  'aaabbbccc' → 'a3b3c3'")
    print()
    
    while True:
        try:
            # Get user input
            user_input = input("Enter a string to compress (or 'quit' to exit): ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if not user_input:
                print("⚠️  Please enter a non-empty string.")
                continue
            
            # Compress the string
            compressed = compressedString(user_input)
            
            # Show results
            print(f"📝 Original:   '{user_input}' (length: {len(user_input)})")
            print(f"🗜️  Compressed: '{compressed}' (length: {len(compressed)})")
            
            # Calculate compression ratio
            if len(user_input) > 0:
                ratio = len(compressed) / len(user_input)
                if ratio < 1:
                    print(f"📊 Compression: {(1-ratio)*100:.1f}% size reduction")
                elif ratio > 1:
                    print(f"📊 Expansion: {(ratio-1)*100:.1f}% size increase")
                else:
                    print("📊 No size change")
            
            print("-" * 50)
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
